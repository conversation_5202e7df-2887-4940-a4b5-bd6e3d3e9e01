from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.exceptions import RequestValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, delete, update
from sqlalchemy.orm import selectinload
from typing import List, Optional
from database.database import get_db
from models.image import ImageCategory, Image
from schemas.image_category import (
    ImageCategoryCreate, ImageCategoryUpdate, ImageCategoryResponse,
    ImageCategoryTree, CategorySortRequest, CategoryStatsResponse
)
from utils.auth import get_current_user
import re
import json

router = APIRouter(prefix="/image-categories", tags=["图片分类"])


def validate_slug(slug: str) -> str:
    """验证并清理slug"""
    # 只允许字母、数字、连字符和下划线
    slug = re.sub(r'[^a-zA-Z0-9\-_]', '', slug.lower())
    if not slug:
        raise HTTPException(status_code=400, detail="Invalid slug format")
    return slug


async def update_category_image_count(db: AsyncSession, category_id: int):
    """更新分类的图片数量"""
    if category_id is None:
        return

    # 计算该分类的实际图片数量
    count_result = await db.execute(
        select(func.count(Image.id)).where(Image.category_id == category_id)
    )
    actual_count = count_result.scalar() or 0

    # 更新分类的图片数量
    await db.execute(
        update(ImageCategory)
        .where(ImageCategory.id == category_id)
        .values(image_count=actual_count)
    )


async def get_category_depth(db: AsyncSession, category_id: int) -> int:
    """获取分类的层级深度"""
    depth = 0
    current_id = category_id
    visited = set()

    while current_id and current_id not in visited:
        visited.add(current_id)
        depth += 1

        # 防止无限循环
        if depth > 10:
            break

        # 获取父分类ID
        result = await db.execute(
            select(ImageCategory.parent_id).where(ImageCategory.id == current_id)
        )
        parent_id = result.scalar()
        current_id = parent_id

    return depth


async def check_circular_reference(db: AsyncSession, category_id: int, parent_id: int) -> bool:
    """检查是否会形成循环引用"""
    if category_id == parent_id:
        return True

    # 获取所有子分类ID
    visited = set()

    async def get_all_children(cat_id: int) -> List[int]:
        if cat_id in visited:
            return []
        visited.add(cat_id)

        result = await db.execute(
            select(ImageCategory.id).where(ImageCategory.parent_id == cat_id)
        )
        children = result.scalars().all()

        all_children = list(children)
        for child_id in children:
            all_children.extend(await get_all_children(child_id))

        return all_children

    children_ids = await get_all_children(category_id)
    return parent_id in children_ids


@router.get("/", response_model=List[ImageCategoryResponse])
async def get_categories(
    include_inactive: bool = Query(False, description="是否包含未启用的分类"),
    parent_id: Optional[int] = Query(None, description="父分类ID，为空则获取顶级分类"),
    db: AsyncSession = Depends(get_db)
):
    """获取图片分类列表"""
    query = select(ImageCategory).options(
        selectinload(ImageCategory.parent),
        selectinload(ImageCategory.children),
        selectinload(ImageCategory.cover_image)
    )
    
    # 过滤条件
    conditions = []
    if not include_inactive:
        conditions.append(ImageCategory.is_active == True)
    
    if parent_id is not None:
        conditions.append(ImageCategory.parent_id == parent_id)
    else:
        conditions.append(ImageCategory.parent_id.is_(None))
    
    if conditions:
        query = query.where(and_(*conditions))
    
    query = query.order_by(ImageCategory.sort_order, ImageCategory.name)
    
    result = await db.execute(query)
    categories = result.scalars().all()
    
    return categories


@router.get("/tree", response_model=List[ImageCategoryTree])
async def get_category_tree(
    include_inactive: bool = Query(False, description="是否包含未启用的分类"),
    db: AsyncSession = Depends(get_db)
):
    """获取树形分类结构"""
    query = select(ImageCategory)
    
    if not include_inactive:
        query = query.where(ImageCategory.is_active == True)
    
    query = query.order_by(ImageCategory.sort_order, ImageCategory.name)
    
    result = await db.execute(query)
    all_categories = result.scalars().all()

    # 获取每个分类的实际图片数量
    category_image_counts = {}
    for category in all_categories:
        count_result = await db.execute(
            select(func.count(Image.id)).where(Image.category_id == category.id)
        )
        category_image_counts[category.id] = count_result.scalar() or 0

    # 构建树形结构
    category_dict = {cat.id: cat for cat in all_categories}
    tree = []
    
    for category in all_categories:
        if category.parent_id is None:
            # 顶级分类
            tree_node = ImageCategoryTree(
                id=category.id,
                name=category.name,
                slug=category.slug,
                color=category.color,
                icon=category.icon,
                image_count=category_image_counts.get(category.id, 0),
                is_system=category.is_system,
                is_active=category.is_active,
                children=[]
            )
            
            # 添加子分类
            for child in all_categories:
                if child.parent_id == category.id:
                    child_node = ImageCategoryTree(
                        id=child.id,
                        name=child.name,
                        slug=child.slug,
                        color=child.color,
                        icon=child.icon,
                        image_count=category_image_counts.get(child.id, 0),
                        is_system=child.is_system,
                        is_active=child.is_active,
                        children=[]
                    )
                    tree_node.children.append(child_node)
            
            tree.append(tree_node)
    
    return tree


@router.get("/stats", response_model=CategoryStatsResponse)
async def get_category_stats(db: AsyncSession = Depends(get_db)):
    """获取分类统计信息"""
    # 总分类数
    total_result = await db.execute(select(func.count(ImageCategory.id)))
    total_categories = total_result.scalar()
    
    # 启用的分类数
    active_result = await db.execute(
        select(func.count(ImageCategory.id)).where(ImageCategory.is_active == True)
    )
    active_categories = active_result.scalar()
    
    # 系统分类数
    system_result = await db.execute(
        select(func.count(ImageCategory.id)).where(ImageCategory.is_system == True)
    )
    system_categories = system_result.scalar()
    
    # 自定义分类数
    custom_categories = total_categories - system_categories
    
    # 有图片的分类数
    with_images_result = await db.execute(
        select(func.count(ImageCategory.id)).where(ImageCategory.image_count > 0)
    )
    categories_with_images = with_images_result.scalar()
    
    # 分类中的总图片数
    total_images_result = await db.execute(
        select(func.sum(ImageCategory.image_count))
    )
    total_images_in_categories = total_images_result.scalar() or 0

    # 实际的总图片数（包括未分类的图片）
    actual_total_images_result = await db.execute(
        select(func.count(Image.id))
    )
    total_images = actual_total_images_result.scalar() or 0

    return CategoryStatsResponse(
        total_categories=total_categories,
        active_categories=active_categories,
        system_categories=system_categories,
        custom_categories=custom_categories,
        categories_with_images=categories_with_images,
        total_images_in_categories=total_images_in_categories,
        total_images=total_images
    )


@router.post("/", response_model=ImageCategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_data: ImageCategoryCreate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """创建图片分类"""
    print(f"Received category data: {category_data}")
    print(f"Category data dict: {category_data.model_dump()}")

    # 验证slug
    try:
        original_slug = category_data.slug
        category_data.slug = validate_slug(category_data.slug)
        print(f"Slug validation: '{original_slug}' -> '{category_data.slug}'")
    except Exception as e:
        print(f"Slug validation error: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid slug: {str(e)}")

    # 检查slug是否已存在
    existing = await db.execute(
        select(ImageCategory).where(ImageCategory.slug == category_data.slug)
    )
    if existing.scalars().first():
        raise HTTPException(status_code=400, detail="Slug already exists")
    
    # 验证父分类是否存在并检查层级深度
    if category_data.parent_id:
        parent_result = await db.execute(
            select(ImageCategory).where(ImageCategory.id == category_data.parent_id)
        )
        parent_category = parent_result.scalars().first()
        if not parent_category:
            raise HTTPException(status_code=404, detail="Parent category not found")

        # 检查层级深度（最大5层）
        depth = await get_category_depth(db, category_data.parent_id)
        if depth >= 5:
            raise HTTPException(status_code=400, detail="Category hierarchy cannot exceed 5 levels")
    
    # 创建分类
    db_category = ImageCategory(**category_data.model_dump())
    db.add(db_category)
    await db.commit()
    await db.refresh(db_category)

    # 手动构建响应数据，避免关联查询问题
    response_data = {
        "id": db_category.id,
        "name": db_category.name,
        "slug": db_category.slug,
        "description": db_category.description,
        "parent_id": db_category.parent_id,
        "color": db_category.color,
        "icon": db_category.icon,
        "cover_image_id": db_category.cover_image_id,
        "sort_order": db_category.sort_order,
        "is_system": db_category.is_system,
        "is_active": db_category.is_active,
        "image_count": db_category.image_count,
        "created_at": db_category.created_at,
        "updated_at": db_category.updated_at,
        "parent": None,
        "children": [],
        "cover_image": None
    }

    return response_data


@router.get("/{category_id}", response_model=ImageCategoryResponse)
async def get_category(
    category_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取单个分类详情"""
    query = select(ImageCategory).options(
        selectinload(ImageCategory.parent),
        selectinload(ImageCategory.children),
        selectinload(ImageCategory.cover_image)
    ).where(ImageCategory.id == category_id)
    
    result = await db.execute(query)
    category = result.scalars().first()
    
    if not category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    return category


@router.put("/{category_id}", response_model=ImageCategoryResponse)
async def update_category(
    category_id: int,
    category_data: ImageCategoryUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """更新图片分类"""
    # 获取现有分类
    result = await db.execute(select(ImageCategory).where(ImageCategory.id == category_id))
    db_category = result.scalars().first()
    
    if not db_category:
        raise HTTPException(status_code=404, detail="Category not found")
    
    # 系统分类的某些字段不能修改
    if db_category.is_system:
        restricted_fields = ['slug', 'is_system']
        for field in restricted_fields:
            if hasattr(category_data, field) and getattr(category_data, field) is not None:
                raise HTTPException(status_code=400, detail=f"Cannot modify {field} for system categories")
    
    # 验证slug（如果提供）
    if category_data.slug:
        category_data.slug = validate_slug(category_data.slug)
        # 检查slug是否已被其他分类使用
        existing = await db.execute(
            select(ImageCategory).where(
                and_(ImageCategory.slug == category_data.slug, ImageCategory.id != category_id)
            )
        )
        if existing.scalars().first():
            raise HTTPException(status_code=400, detail="Slug already exists")
    
    # 验证父分类
    if category_data.parent_id:
        if category_data.parent_id == category_id:
            raise HTTPException(status_code=400, detail="Category cannot be its own parent")

        parent_result = await db.execute(
            select(ImageCategory).where(ImageCategory.id == category_data.parent_id)
        )
        if not parent_result.scalars().first():
            raise HTTPException(status_code=404, detail="Parent category not found")

        # 检查循环引用
        is_circular = await check_circular_reference(db, category_id, category_data.parent_id)
        if is_circular:
            raise HTTPException(status_code=400, detail="Cannot create circular reference")

        # 检查层级深度
        depth = await get_category_depth(db, category_data.parent_id)
        if depth >= 5:
            raise HTTPException(status_code=400, detail="Category hierarchy cannot exceed 5 levels")
    
    # 更新字段
    update_data = category_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    await db.commit()
    await db.refresh(db_category)

    return db_category


@router.delete("/{category_id}")
async def delete_category(
    category_id: int,
    force: bool = Query(False, description="强制删除（即使有图片）"),
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除图片分类"""
    # 获取分类
    result = await db.execute(select(ImageCategory).where(ImageCategory.id == category_id))
    db_category = result.scalars().first()

    if not db_category:
        raise HTTPException(status_code=404, detail="Category not found")

    # 系统分类不能删除
    if db_category.is_system:
        raise HTTPException(status_code=400, detail="Cannot delete system categories")

    # 检查是否有子分类
    children_result = await db.execute(
        select(func.count(ImageCategory.id)).where(ImageCategory.parent_id == category_id)
    )
    children_count = children_result.scalar()

    if children_count > 0:
        raise HTTPException(status_code=400, detail="Cannot delete category with subcategories")

    # 检查是否有图片
    if db_category.image_count > 0 and not force:
        raise HTTPException(
            status_code=400,
            detail=f"Category has {db_category.image_count} images. Use force=true to delete anyway"
        )

    # 如果强制删除，将图片的分类设为null
    if force and db_category.image_count > 0:
        await db.execute(
            update(Image).where(Image.category_id == category_id).values(category_id=None)
        )

    # 删除分类
    await db.delete(db_category)
    await db.commit()

    return {"message": "Category deleted successfully"}


@router.put("/sort", response_model=dict)
async def sort_categories(
    sort_request: CategorySortRequest,
    db: AsyncSession = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """批量更新分类排序"""
    try:
        for item in sort_request.category_orders:
            category_id = item.get("id")
            sort_order = item.get("sort_order")

            if category_id and sort_order is not None:
                await db.execute(
                    update(ImageCategory)
                    .where(ImageCategory.id == category_id)
                    .values(sort_order=sort_order)
                )

        await db.commit()
        return {"message": "Categories sorted successfully"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Error sorting categories: {str(e)}")


@router.get("/{category_id}/images")
async def get_category_images(
    category_id: int,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db)
):
    """获取分类下的图片"""
    # 验证分类是否存在
    category_result = await db.execute(
        select(ImageCategory).where(ImageCategory.id == category_id)
    )
    category = category_result.scalars().first()

    if not category:
        raise HTTPException(status_code=404, detail="Category not found")

    # 计算偏移量
    offset = (page - 1) * page_size

    # 获取图片
    images_query = select(Image).where(Image.category_id == category_id).offset(offset).limit(page_size)
    images_result = await db.execute(images_query)
    images = images_result.scalars().all()

    # 获取总数
    count_result = await db.execute(
        select(func.count(Image.id)).where(Image.category_id == category_id)
    )
    total = count_result.scalar()

    return {
        "images": images,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": (total + page_size - 1) // page_size
    }
