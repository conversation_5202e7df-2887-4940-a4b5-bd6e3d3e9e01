import React, { useState } from 'react';
import {
  Modal, Upload, Form, Input, Select, Button, Progress, message,
  Row, Col, Card, Space, Tag, Divider, Typography, Alert, Collapse,
  Switch, Slider, InputNumber
} from 'antd';
import {
  InboxOutlined, PlusOutlined, DeleteOutlined, EyeOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, SettingOutlined
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import axiosInstance from '../../api/axiosInstance';

const { Dragger } = Upload;
const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface ImageUploaderProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  categories?: any[];
}

interface ProcessingOptions {
  enable_compression: boolean;
  enable_webp: boolean;
  enable_thumbnail: boolean;
  quality: number;
  max_width: number;
  max_height: number;
  thumbnail_size: number;
}

interface UploadItem {
  file: UploadFile;
  display_name: string;
  description: string;
  category_id?: number;

  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  visible,
  onClose,
  onSuccess,
  categories = []
}) => {
  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);
  const [uploading, setUploading] = useState(false);
  const [globalSettings, setGlobalSettings] = useState({
    category_id: undefined
  });
  const [processingOptions, setProcessingOptions] = useState<ProcessingOptions>({
    enable_compression: true,
    enable_webp: true,
    enable_thumbnail: true,
    quality: 85,
    max_width: 1920,
    max_height: 1080,
    thumbnail_size: 300
  });
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  const handleFileSelect: UploadProps['onChange'] = ({ fileList }) => {
    const newItems: UploadItem[] = fileList.map(file => {
      const existingItem = uploadItems.find(item => item.file.uid === file.uid);
      if (existingItem) {
        return existingItem;
      }

      // 生成显示名称（去掉扩展名）
      const displayName = file.name ? file.name.replace(/\.[^/.]+$/, '') : 'Untitled';

      return {
        file,
        display_name: displayName,
        description: '',
        category_id: globalSettings.category_id,

        status: 'pending',
        progress: 0
      };
    });

    setUploadItems(newItems);
  };

  const updateUploadItem = (uid: string, updates: Partial<UploadItem>) => {
    setUploadItems(prev => prev.map(item => 
      item.file.uid === uid ? { ...item, ...updates } : item
    ));
  };

  const removeUploadItem = (uid: string) => {
    setUploadItems(prev => prev.filter(item => item.file.uid !== uid));
  };

  const applyGlobalSettings = () => {
    setUploadItems(prev => prev.map(item => ({
      ...item,
      category_id: globalSettings.category_id
    })));
    message.success('已应用全局设置到所有图片');
  };

  const uploadSingleImage = async (item: UploadItem): Promise<void> => {
    const formData = new FormData();
    formData.append('file', item.file.originFileObj as File);
    formData.append('display_name', item.display_name);
    formData.append('description', item.description);

    if (item.category_id) {
      formData.append('category_id', item.category_id.toString());
    }

    // 添加图片处理选项
    formData.append('enable_compression', processingOptions.enable_compression.toString());
    formData.append('enable_webp', processingOptions.enable_webp.toString());
    formData.append('enable_thumbnail', processingOptions.enable_thumbnail.toString());
    formData.append('quality', processingOptions.quality.toString());
    formData.append('max_width', processingOptions.max_width.toString());
    formData.append('max_height', processingOptions.max_height.toString());
    formData.append('thumbnail_size', processingOptions.thumbnail_size.toString());

    try {
      updateUploadItem(item.file.uid, { status: 'uploading', progress: 0 });

      const response = await axiosInstance.post('/images/upload-manager', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const progress = progressEvent.total 
            ? Math.round((progressEvent.loaded * 100) / progressEvent.total)
            : 0;
          updateUploadItem(item.file.uid, { progress });
        }
      });

      updateUploadItem(item.file.uid, { 
        status: 'success', 
        progress: 100 
      });

    } catch (error: any) {
      console.error('Upload error:', error);
      console.error('Error response:', error.response);

      let errorMessage = '上传失败';

      if (error.response?.data) {
        const data = error.response.data;
        if (typeof data.detail === 'string') {
          errorMessage = data.detail;
        } else if (Array.isArray(data.detail)) {
          // 处理验证错误数组
          errorMessage = data.detail.map((err: any) => {
            if (typeof err === 'string') return err;
            if (err.msg) return `${err.loc?.join('.')} ${err.msg}`;
            return JSON.stringify(err);
          }).join('; ');
        } else if (data.detail && typeof data.detail === 'object') {
          errorMessage = JSON.stringify(data.detail);
        } else if (data.message) {
          errorMessage = data.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      updateUploadItem(item.file.uid, {
        status: 'error',
        error: errorMessage,
        progress: 0
      });
      throw error;
    }
  };

  const handleUploadAll = async () => {
    if (uploadItems.length === 0) {
      message.warning('请先选择要上传的图片');
      return;
    }

    setUploading(true);
    let successCount = 0;
    let errorCount = 0;

    try {
      // 并发上传，但限制并发数
      const concurrency = 3;
      const chunks = [];
      for (let i = 0; i < uploadItems.length; i += concurrency) {
        chunks.push(uploadItems.slice(i, i + concurrency));
      }

      for (const chunk of chunks) {
        const promises = chunk.map(async (item) => {
          try {
            await uploadSingleImage(item);
            successCount++;
          } catch (error) {
            errorCount++;
          }
        });

        await Promise.all(promises);
      }

      if (successCount > 0) {
        message.success(`成功上传 ${successCount} 张图片`);
        onSuccess();
      }

      if (errorCount > 0) {
        message.error(`${errorCount} 张图片上传失败`);
      }

      // 如果全部成功，关闭对话框
      if (errorCount === 0) {
        handleClose();
      }

    } finally {
      setUploading(false);
    }
  };

  const handleClose = () => {
    if (uploading) {
      Modal.confirm({
        title: '确认关闭',
        content: '图片正在上传中，确定要关闭吗？',
        onOk: () => {
          setUploadItems([]);
          onClose();
        }
      });
    } else {
      setUploadItems([]);
      onClose();
    }
  };

  const uploadProps: UploadProps = {
    multiple: true,
    accept: 'image/*',
    beforeUpload: () => false, // 阻止自动上传
    onChange: handleFileSelect,
    fileList: uploadItems.map(item => item.file),
    showUploadList: false
  };

  const getCategoryName = (categoryId?: number) => {
    if (!categoryId) return '未分类';
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name || '未知分类';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return null;
    }
  };

  return (
    <Modal
      title="上传图片"
      open={visible}
      onCancel={handleClose}
      width={1000}
      footer={[
        <Button key="cancel" onClick={handleClose} disabled={uploading}>
          取消
        </Button>,
        <Button
          key="upload"
          type="primary"
          onClick={handleUploadAll}
          loading={uploading}
          disabled={uploadItems.length === 0}
        >
          上传全部 ({uploadItems.length})
        </Button>
      ]}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* 文件选择区域 */}
        <Card title="选择图片" style={{ marginBottom: 16 }}>
          <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个或批量上传。支持 JPG、PNG、GIF、WebP 等格式
            </p>
          </Dragger>
        </Card>

        {/* 全局设置 */}
        {uploadItems.length > 0 && (
          <Card
            title="全局设置"
            size="small"
            style={{ marginBottom: 16 }}
            extra={
              <Button size="small" onClick={applyGlobalSettings}>
                应用到全部
              </Button>
            }
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="默认分类" style={{ marginBottom: 8 }}>
                  <Select
                    placeholder="选择分类"
                    allowClear
                    value={globalSettings.category_id}
                    onChange={(value) => setGlobalSettings(prev => ({ ...prev, category_id: value }))}
                  >
                    {categories.map(category => (
                      <Select.Option key={category.id} value={category.id}>
                        {category.name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item style={{ marginBottom: 8 }}>
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                    size="small"
                  >
                    {showAdvancedOptions ? '隐藏' : '显示'}高级选项
                  </Button>
                </Form.Item>
              </Col>
            </Row>

            {/* 高级处理选项 */}
            {showAdvancedOptions && (
              <Card size="small" title="图片处理选项" style={{ marginTop: 16 }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Switch
                          checked={processingOptions.enable_compression}
                          onChange={(checked) => setProcessingOptions(prev => ({ ...prev, enable_compression: checked }))}
                        />
                        <span style={{ marginLeft: 8 }}>启用压缩</span>
                      </div>
                      <div>
                        <Switch
                          checked={processingOptions.enable_webp}
                          onChange={(checked) => setProcessingOptions(prev => ({ ...prev, enable_webp: checked }))}
                        />
                        <span style={{ marginLeft: 8 }}>生成WebP</span>
                      </div>
                      <div>
                        <Switch
                          checked={processingOptions.enable_thumbnail}
                          onChange={(checked) => setProcessingOptions(prev => ({ ...prev, enable_thumbnail: checked }))}
                        />
                        <span style={{ marginLeft: 8 }}>生成缩略图</span>
                      </div>
                    </Space>
                  </Col>
                  <Col span={8}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text>质量: {processingOptions.quality}%</Text>
                        <Slider
                          min={10}
                          max={100}
                          value={processingOptions.quality}
                          onChange={(value) => setProcessingOptions(prev => ({ ...prev, quality: value }))}
                        />
                      </div>
                      <div>
                        <Text>缩略图尺寸:</Text>
                        <InputNumber
                          min={100}
                          max={800}
                          value={processingOptions.thumbnail_size}
                          onChange={(value) => setProcessingOptions(prev => ({ ...prev, thumbnail_size: value || 300 }))}
                          addonAfter="px"
                        />
                      </div>
                    </Space>
                  </Col>
                  <Col span={8}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text>最大宽度:</Text>
                        <InputNumber
                          min={800}
                          max={4000}
                          value={processingOptions.max_width}
                          onChange={(value) => setProcessingOptions(prev => ({ ...prev, max_width: value || 1920 }))}
                          addonAfter="px"
                        />
                      </div>
                      <div>
                        <Text>最大高度:</Text>
                        <InputNumber
                          min={600}
                          max={3000}
                          value={processingOptions.max_height}
                          onChange={(value) => setProcessingOptions(prev => ({ ...prev, max_height: value || 1080 }))}
                          addonAfter="px"
                        />
                      </div>
                    </Space>
                  </Col>
                </Row>
              </Card>
            )}
          </Card>
        )}

        {/* 上传列表 */}
        {uploadItems.length > 0 && (
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="默认分类" style={{ marginBottom: 8 }}>
                  <Select
                    placeholder="选择分类"
                    allowClear
                    value={globalSettings.category_id}
                    onChange={(value) => setGlobalSettings(prev => ({ ...prev, category_id: value }))}
                  >
                    {categories.map(category => (
                      <Option key={category.id} value={category.id}>
                        <Space>
                          {category.icon && <span>{category.icon}</span>}
                          {category.name}
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>

            </Row>
          </Card>
        )}

        {/* 图片列表 */}
        {uploadItems.length > 0 && (
          <Card title={`图片列表 (${uploadItems.length})`}>
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {uploadItems.map((item, index) => (
                <Card
                  key={item.file.uid}
                  size="small"
                  style={{ marginBottom: 8 }}
                  title={
                    <Space>
                      <span>#{index + 1}</span>
                      {getStatusIcon(item.status)}
                      <Text strong>{item.file.name}</Text>
                      {item.status === 'uploading' && (
                        <Progress
                          percent={item.progress}
                          size="small"
                          style={{ width: 100 }}
                        />
                      )}
                    </Space>
                  }
                  extra={
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeUploadItem(item.file.uid)}
                      disabled={uploading}
                    />
                  }
                >
                  {item.status === 'error' && (
                    <Alert
                      message={item.error}
                      type="error"
                      size="small"
                      style={{ marginBottom: 8 }}
                    />
                  )}
                  
                  <Row gutter={16}>
                    <Col span={8}>
                      <Input
                        placeholder="图片名称"
                        value={item.display_name}
                        onChange={(e) => updateUploadItem(item.file.uid, { display_name: e.target.value })}
                        disabled={uploading}
                      />
                    </Col>
                    <Col span={24}>
                      <Select
                        placeholder="选择分类"
                        allowClear
                        value={item.category_id}
                        onChange={(value) => updateUploadItem(item.file.uid, { category_id: value })}
                        disabled={uploading}
                        style={{ width: '100%' }}
                      >
                        {categories.map(category => (
                          <Option key={category.id} value={category.id}>
                            <Space>
                              {category.icon && <span>{category.icon}</span>}
                              {category.name}
                            </Space>
                          </Option>
                        ))}
                      </Select>
                    </Col>

                  </Row>
                  
                  <div style={{ marginTop: 8 }}>
                    <TextArea
                      placeholder="图片描述（可选）"
                      value={item.description}
                      onChange={(e) => updateUploadItem(item.file.uid, { description: e.target.value })}
                      disabled={uploading}
                      rows={2}
                    />
                  </div>
                </Card>
              ))}
            </div>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default ImageUploader;
