import React, { useState, useCallback, useRef } from 'react';
import {
  Modal, Tree, Button, Form, Input, Select, ColorPicker, Space,
  Card, Row, Col, Typography, Tag, Popconfirm, message, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, FolderOutlined,
  SaveOutlined, CloseOutlined, DragOutlined, LoadingOutlined, CheckCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../../api/axiosInstance';
import { useCategorySlug } from '../../hooks/useSlug';
import { generateSlug } from '../../utils/slugManager';

const { Title, Text } = Typography;
const { TreeNode } = Tree;

interface CategoryData {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  image_count: number;
  is_system: boolean;
  is_active: boolean;
  parent_id?: number;
  children?: CategoryData[];
}

interface CategoryManagerProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// 辅助函数：检查循环引用
const checkCircularReference = (parentId: number, categoryId: number, categories: CategoryData[]): boolean => {
  const findCategory = (id: number): CategoryData | undefined => {
    const findInTree = (cats: CategoryData[]): CategoryData | undefined => {
      for (const cat of cats) {
        if (cat.id === id) return cat;
        if (cat.children) {
          const found = findInTree(cat.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    return findInTree(categories);
  };

  const isDescendant = (ancestorId: number, descendantId: number): boolean => {
    const ancestor = findCategory(ancestorId);
    if (!ancestor || !ancestor.children) return false;

    for (const child of ancestor.children) {
      if (child.id === descendantId) return true;
      if (isDescendant(child.id, descendantId)) return true;
    }
    return false;
  };

  return isDescendant(categoryId, parentId);
};

// 辅助函数：扁平化分类树
const flattenCategories = (categories: CategoryData[]): CategoryData[] => {
  const result: CategoryData[] = [];
  const flatten = (cats: CategoryData[]) => {
    cats.forEach(cat => {
      result.push(cat);
      if (cat.children) {
        flatten(cat.children);
      }
    });
  };
  flatten(categories);
  return result;
};

// 辅助函数：计算分类层级深度
const getCategoryDepth = (categoryId: number, categories: CategoryData[]): number => {
  const findCategory = (id: number): CategoryData | undefined => {
    const findInTree = (cats: CategoryData[]): CategoryData | undefined => {
      for (const cat of cats) {
        if (cat.id === id) return cat;
        if (cat.children) {
          const found = findInTree(cat.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    return findInTree(categories);
  };

  const calculateDepth = (id: number, visited = new Set<number>()): number => {
    if (visited.has(id)) return 0; // 防止循环引用
    visited.add(id);

    const category = findCategory(id);
    if (!category || !category.parent_id) return 1;

    return 1 + calculateDepth(category.parent_id, visited);
  };

  return calculateDepth(categoryId);
};

// 辅助函数：获取分类的所有子分类ID
const getAllChildrenIds = (categoryId: number, categories: CategoryData[]): number[] => {
  const result: number[] = [];
  const allCategories = flattenCategories(categories);

  const findChildren = (parentId: number) => {
    allCategories.forEach(cat => {
      if (cat.parent_id === parentId) {
        result.push(cat.id);
        findChildren(cat.id); // 递归查找子分类
      }
    });
  };

  findChildren(categoryId);
  return result;
};

const CategoryManager: React.FC<CategoryManagerProps> = ({
  visible,
  onClose,
  onSuccess
}) => {
  const [selectedCategory, setSelectedCategory] = useState<CategoryData | null>(null);
  const [editMode, setEditMode] = useState<'create' | 'edit' | null>(null);
  const [form] = Form.useForm();
  const queryClient = useQueryClient();

  // 使用slug Hook
  const slugHook = useCategorySlug({
    useBackend: true,
    autoGenerate: false
  });

  // 状态管理
  const [slugValidation, setSlugValidation] = useState<{
    isValidating: boolean;
    isValid: boolean;
    isUnique: boolean;
    message: string;
  }>({
    isValidating: false,
    isValid: true,
    isUnique: true,
    message: ''
  });

  // 防抖验证的引用
  const slugValidationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖的slug验证函数
  const debouncedValidateSlug = useCallback((slug: string) => {
    if (slugValidationTimeoutRef.current) {
      clearTimeout(slugValidationTimeoutRef.current);
    }

    slugValidationTimeoutRef.current = setTimeout(() => {
      validateSlugUniqueness(slug);
    }, 500); // 500ms防抖
  }, [categories, editMode, selectedCategory]);

  // 获取分类列表
  const { data: categories, isLoading } = useQuery({
    queryKey: ['image-categories-tree'],
    queryFn: async () => {
      const response = await axiosInstance.get('/image-categories/tree?include_inactive=true');
      return response.data;
    },
    enabled: visible
  });

  // 创建分类
  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      // 数据验证
      if (!data.name?.trim()) {
        throw new Error('分类名称不能为空');
      }
      if (!data.slug?.trim()) {
        throw new Error('分类标识不能为空');
      }

      console.log('Creating category with data:', data);
      const response = await axiosInstance.post('/image-categories/', data);
      return response.data;
    },
    onSuccess: (data) => {
      message.success(`分类 "${data.name}" 创建成功`);
      queryClient.invalidateQueries({ queryKey: ['image-categories-tree'] });
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
      setEditMode(null);
      form.resetFields();
      onSuccess();
    },
    onError: (error: any) => {
      console.error('Create category error:', error);
      const errorMessage = error.response?.data?.detail || error.message || '创建失败';

      // 处理特定错误类型
      if (errorMessage.includes('slug')) {
        message.error('分类标识已存在或格式不正确，请修改后重试');
      } else if (errorMessage.includes('name')) {
        message.error('分类名称已存在或格式不正确');
      } else {
        message.error(`创建失败: ${errorMessage}`);
      }
    }
  });

  // 更新分类
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      // 数据验证
      if (!data.name?.trim()) {
        throw new Error('分类名称不能为空');
      }
      if (!data.slug?.trim()) {
        throw new Error('分类标识不能为空');
      }

      const response = await axiosInstance.put(`/image-categories/${id}`, data);
      return response.data;
    },
    onSuccess: (data) => {
      message.success(`分类 "${data.name}" 更新成功`);
      queryClient.invalidateQueries({ queryKey: ['image-categories-tree'] });
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
      setEditMode(null);
      form.resetFields();
      setSelectedCategory(null);
      onSuccess();
    },
    onError: (error: any) => {
      console.error('Update category error:', error);
      const errorMessage = error.response?.data?.detail || error.message || '更新失败';

      // 处理特定错误类型
      if (errorMessage.includes('slug')) {
        message.error('分类标识已存在或格式不正确，请修改后重试');
      } else if (errorMessage.includes('name')) {
        message.error('分类名称已存在或格式不正确');
      } else {
        message.error(`更新失败: ${errorMessage}`);
      }
    }
  });

  // 删除分类
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await axiosInstance.delete(`/image-categories/${id}`);
    },
    onSuccess: () => {
      message.success('分类删除成功');
      queryClient.invalidateQueries({ queryKey: ['image-categories-tree'] });
      queryClient.invalidateQueries({ queryKey: ['image-categories'] });
      setSelectedCategory(null);
      setEditMode(null);
      form.resetFields();
      onSuccess();
    },
    onError: (error: any) => {
      console.error('Delete category error:', error);
      const errorMessage = error.response?.data?.detail || error.message || '删除失败';

      // 处理特定错误类型
      if (errorMessage.includes('图片')) {
        message.error('该分类下还有图片，请先移动或删除图片后再删除分类');
      } else if (errorMessage.includes('子分类')) {
        message.error('该分类下还有子分类，请先删除子分类');
      } else {
        message.error(`删除失败: ${errorMessage}`);
      }
    }
  });

  const handleCreateCategory = () => {
    setEditMode('create');
    form.resetFields();
    form.setFieldsValue({
      color: '#1890ff',
      is_active: true,
      parent_id: selectedCategory?.id
    });
  };

  const handleEditCategory = () => {
    if (!selectedCategory) return;
    
    setEditMode('edit');
    form.setFieldsValue({
      name: selectedCategory.name,
      slug: selectedCategory.slug,
      description: selectedCategory.description,
      color: selectedCategory.color,
      icon: selectedCategory.icon,
      is_active: selectedCategory.is_active,
      parent_id: selectedCategory.parent_id
    });
  };

  const handleSave = async () => {
    console.log('handleSave called, editMode:', editMode);

    // 防止重复提交
    if (createMutation.isPending || updateMutation.isPending) {
      return;
    }

    try {
      const values = await form.validateFields();
      console.log('Form validation passed, values:', values);

      // 处理颜色值格式
      if (values.color && typeof values.color === 'object') {
        values.color = values.color.toHexString();
      }

      // 确保slug存在且有效
      if (!values.slug || values.slug.trim() === '') {
        // 如果没有slug，从名称生成一个
        const existingSlugs = categories?.map(cat => cat.slug).filter(Boolean) || [];
        values.slug = generateSlug(values.name, 'image_category', existingSlugs);
      }

      // 验证父分类关系
      if (values.parent_id) {
        // 验证父分类不能是自己
        if (editMode === 'edit' && selectedCategory && values.parent_id === selectedCategory.id) {
          message.error('不能将分类设置为自己的父分类');
          return;
        }

        // 检查是否会形成循环引用
        if (editMode === 'edit' && selectedCategory) {
          const childrenIds = getAllChildrenIds(selectedCategory.id, categories || []);
          if (childrenIds.includes(values.parent_id)) {
            message.error('不能选择子分类作为父分类，这会形成循环引用');
            return;
          }
        }

        // 检查层级深度（最大5层）
        const maxDepth = 5;
        const currentDepth = getCategoryDepth(values.parent_id, categories || []);
        if (currentDepth >= maxDepth) {
          message.error(`分类层级不能超过${maxDepth}层，当前选择的父分类已达到最大层级`);
          return;
        }
      }

      console.log('Submitting values:', values);

      if (editMode === 'create') {
        console.log('Creating category...');
        createMutation.mutate(values);
      } else if (editMode === 'edit' && selectedCategory) {
        console.log('Updating category...');
        updateMutation.mutate({ id: selectedCategory.id, data: values });
      }
    } catch (error) {
      console.error('Validation failed:', error);
      message.error('表单验证失败，请检查输入内容');
    }
  };

  const handleDelete = () => {
    if (!selectedCategory) return;

    if (selectedCategory.is_system) {
      message.error('系统分类不能删除');
      return;
    }

    if (selectedCategory.image_count > 0) {
      message.error('该分类下还有图片，请先移动或删除图片');
      return;
    }

    deleteMutation.mutate(selectedCategory.id);
  };

  // slug验证函数
  const validateSlugUniqueness = async (slug: string) => {
    if (!slug || slug.trim() === '') {
      setSlugValidation({
        isValidating: false,
        isValid: true,
        isUnique: true,
        message: ''
      });
      return;
    }

    setSlugValidation(prev => ({ ...prev, isValidating: true }));

    try {
      // 检查格式
      const isValidFormat = /^[a-z0-9\-_]+$/.test(slug);
      if (!isValidFormat) {
        setSlugValidation({
          isValidating: false,
          isValid: false,
          isUnique: true,
          message: '分类标识只能包含小写字母、数字、连字符和下划线'
        });
        return;
      }

      // 检查唯一性
      const allCategories = flattenCategories(categories || []);
      const existingSlugs = allCategories
        .filter(cat => editMode === 'create' || cat.id !== selectedCategory?.id)
        .map(cat => cat.slug);

      const isUnique = !existingSlugs.includes(slug);

      setSlugValidation({
        isValidating: false,
        isValid: true,
        isUnique,
        message: isUnique ? '分类标识可用' : '分类标识已存在，请使用其他标识'
      });
    } catch (error) {
      setSlugValidation({
        isValidating: false,
        isValid: true,
        isUnique: false,
        message: '验证失败，请重试'
      });
    }
  };



  const renderTreeNodes = (data: CategoryData[]): React.ReactNode => {
    return data.map((category) => (
      <TreeNode
        key={category.id}
        title={
          <Space>
            {category.icon && <span>{category.icon}</span>}
            <span style={{ color: category.color }}>{category.name}</span>
            <Tag color={category.color} size="small">
              {category.image_count}
            </Tag>
            {category.is_system && <Tag size="small">系统</Tag>}
            {!category.is_active && <Tag color="red" size="small">禁用</Tag>}
          </Space>
        }
        value={category.id}
      >
        {category.children && renderTreeNodes(category.children)}
      </TreeNode>
    ));
  };

  const flattenCategories = (categories: CategoryData[]): CategoryData[] => {
    const result: CategoryData[] = [];
    const flatten = (cats: CategoryData[]) => {
      cats.forEach(cat => {
        result.push(cat);
        if (cat.children) {
          flatten(cat.children);
        }
      });
    };
    flatten(categories);
    return result;
  };

  const allCategories = categories ? flattenCategories(categories) : [];

  return (
    <Modal
      title="分类管理"
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
    >
      <Row gutter={24}>
        {/* 左侧：分类树 */}
        <Col span={12}>
          <Card
            title="分类列表"
            extra={
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleCreateCategory}
              >
                新建分类
              </Button>
            }
          >
            {categories && categories.length > 0 ? (
              <Tree
                showLine
                defaultExpandAll
                selectedKeys={selectedCategory ? [selectedCategory.id.toString()] : []}
                onSelect={(selectedKeys, info) => {
                  if (selectedKeys.length > 0) {
                    const categoryId = parseInt(selectedKeys[0] as string);
                    const category = allCategories.find(cat => cat.id === categoryId);
                    setSelectedCategory(category || null);
                  } else {
                    setSelectedCategory(null);
                  }
                }}
              >
                {renderTreeNodes(categories)}
              </Tree>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无分类</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧：分类详情/编辑 */}
        <Col span={12}>
          {editMode ? (
            <Card
              title={editMode === 'create' ? '新建分类' : '编辑分类'}
              extra={
                <Space>
                  <Button
                    size="small"
                    icon={<CloseOutlined />}
                    onClick={() => {
                      setEditMode(null);
                      form.resetFields();
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    icon={<SaveOutlined />}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleSave();
                    }}
                    loading={createMutation.isPending || updateMutation.isPending}
                  >
                    保存
                  </Button>
                </Space>
              }
            >
              <Form
                form={form}
                layout="vertical"
              >
                <Form.Item
                  label="分类名称"
                  name="name"
                  rules={[
                    { required: true, message: '请输入分类名称' },
                    { min: 1, max: 100, message: '分类名称长度应在1-100个字符之间' },
                    { whitespace: true, message: '分类名称不能只包含空格' }
                  ]}
                >
                  <Input
                    placeholder="输入分类名称"
                    maxLength={100}
                    showCount
                    onChange={(e) => {
                      const existingSlugs = allCategories?.map(cat => cat.slug).filter(Boolean) || [];
                      const slug = generateSlug(e.target.value, 'image_category', existingSlugs);
                      form.setFieldsValue({ slug });
                    }}
                  />
                </Form.Item>

                <Form.Item
                  label="分类标识"
                  name="slug"
                  rules={[
                    { required: true, message: '请输入分类标识' },
                    { min: 1, max: 100, message: '分类标识长度应在1-100个字符之间' },
                    { pattern: /^[a-z0-9\-_]+$/, message: '分类标识只能包含小写字母、数字、连字符和下划线' },
                    {
                      validator: async (_, value) => {
                        if (value && !slugValidation.isUnique) {
                          throw new Error(slugValidation.message);
                        }
                      }
                    }
                  ]}
                  validateStatus={
                    slugValidation.isValidating
                      ? 'validating'
                      : !slugValidation.isValid || !slugValidation.isUnique
                        ? 'error'
                        : slugValidation.message && slugValidation.isUnique
                          ? 'success'
                          : ''
                  }
                  help={
                    slugValidation.isValidating
                      ? '正在验证...'
                      : slugValidation.message || undefined
                  }
                >
                  <Input
                    placeholder="分类标识（用于URL）"
                    maxLength={100}
                    showCount
                    onChange={(e) => {
                      const value = e.target.value.toLowerCase();
                      form.setFieldsValue({ slug: value });
                      debouncedValidateSlug(value);
                    }}
                    suffix={
                      slugValidation.isValidating ? (
                        <LoadingOutlined />
                      ) : slugValidation.message ? (
                        slugValidation.isUnique && slugValidation.isValid ? (
                          <CheckCircleOutlined style={{ color: '#52c41a' }} />
                        ) : (
                          <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                        )
                      ) : null
                    }
                  />
                </Form.Item>

                <Form.Item
                  label="分类描述"
                  name="description"
                  rules={[
                    { max: 500, message: '描述长度不能超过500个字符' }
                  ]}
                >
                  <Input.TextArea
                    rows={3}
                    placeholder="输入分类描述（可选）"
                    maxLength={500}
                    showCount
                  />
                </Form.Item>

                <Form.Item label="父分类" name="parent_id">
                  <Select
                    placeholder="选择父分类（可选）"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as any)?.props?.children?.[1]?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {allCategories
                      .filter(cat => {
                        // 创建模式：排除当前选中的分类
                        if (editMode === 'create') return true;
                        // 编辑模式：排除自己和自己的子分类
                        if (editMode === 'edit' && selectedCategory) {
                          if (cat.id === selectedCategory.id) return false;
                          // 检查是否是子分类
                          return !checkCircularReference(cat.id, selectedCategory.id, categories || []);
                        }
                        return true;
                      })
                      .map(category => (
                        <Select.Option key={category.id} value={category.id}>
                          <Space>
                            {category.icon && <span>{category.icon}</span>}
                            <span>{category.name}</span>
                            {category.is_system && <Tag size="small" color="blue">系统</Tag>}
                          </Space>
                        </Select.Option>
                      ))}
                  </Select>
                </Form.Item>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="分类颜色"
                      name="color"
                      rules={[{ required: true, message: '请选择分类颜色' }]}
                    >
                      <ColorPicker showText />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="分类图标" name="icon">
                      <Input placeholder="图标（可选）" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="状态"
                  name="is_active"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select>
                    <Select.Option value={true}>启用</Select.Option>
                    <Select.Option value={false}>禁用</Select.Option>
                  </Select>
                </Form.Item>
              </Form>
            </Card>
          ) : selectedCategory ? (
            <Card
              title="分类详情"
              extra={
                <Space>
                  <Button
                    size="small"
                    icon={<EditOutlined />}
                    onClick={handleEditCategory}
                    disabled={selectedCategory.is_system}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    title="确定要删除这个分类吗？"
                    description="删除后不可恢复，请确认该分类下没有图片。"
                    onConfirm={handleDelete}
                    okText="删除"
                    cancelText="取消"
                    disabled={selectedCategory.is_system || selectedCategory.image_count > 0}
                  >
                    <Button
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      disabled={selectedCategory.is_system || selectedCategory.image_count > 0}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              }
            >
              <div>
                <Title level={4} style={{ color: selectedCategory.color }}>
                  {selectedCategory.icon && <span>{selectedCategory.icon} </span>}
                  {selectedCategory.name}
                </Title>
                
                {selectedCategory.description && (
                  <Text type="secondary">{selectedCategory.description}</Text>
                )}

                <Divider />

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Text strong>分类标识：</Text>
                    <br />
                    <Text code>{selectedCategory.slug}</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>图片数量：</Text>
                    <br />
                    <Tag color={selectedCategory.color}>
                      {selectedCategory.image_count} 张
                    </Tag>
                  </Col>
                  <Col span={12}>
                    <Text strong>分类颜色：</Text>
                    <br />
                    <Tag color={selectedCategory.color}>
                      {selectedCategory.color}
                    </Tag>
                  </Col>
                  <Col span={12}>
                    <Text strong>状态：</Text>
                    <br />
                    <Tag color={selectedCategory.is_active ? 'green' : 'red'}>
                      {selectedCategory.is_active ? '启用' : '禁用'}
                    </Tag>
                  </Col>
                  {selectedCategory.is_system && (
                    <Col span={24}>
                      <Tag color="blue">系统分类</Tag>
                      <Text type="secondary">（系统分类不可删除和修改部分属性）</Text>
                    </Col>
                  )}
                </Row>
              </div>
            </Card>
          ) : (
            <Card>
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <FolderOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <br />
                <Text type="secondary">请选择一个分类查看详情</Text>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default CategoryManager;
